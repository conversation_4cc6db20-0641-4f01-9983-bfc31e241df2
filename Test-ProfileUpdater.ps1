# Test Script for PowerShell Profile Updater
# This script demonstrates the functionality and provides basic testing

# Load the main function
. .\Update-PowerShellProfile.ps1

function Test-ProfileUpdater {
    <#
    .SYNOPSIS
        Tests the PowerShell Profile Updater functionality
    
    .DESCRIPTION
        Runs various tests to ensure the profile updater works correctly
    #>
    
    Write-Host "🧪 Testing PowerShell Profile Updater" -ForegroundColor Cyan
    Write-Host "=====================================" -ForegroundColor Cyan
    
    # Test 1: Check if function is loaded
    Write-Host "`n1️⃣ Testing function availability..." -ForegroundColor Yellow
    if (Get-Command Update-PowerShellProfile -ErrorAction SilentlyContinue) {
        Write-Host "✅ Update-PowerShellProfile function is available" -ForegroundColor Green
    } else {
        Write-Host "❌ Update-PowerShellProfile function not found" -ForegroundColor Red
        return
    }
    
    # Test 2: Test helper functions
    Write-Host "`n2️⃣ Testing helper functions..." -ForegroundColor Yellow
    
    # Test version parsing
    $testContent = @"
# Version 1.2.3
# This is a test profile
Write-Host "Hello World"
"@
    
    $version = Get-ProfileVersion -Content $testContent
    if ($version -eq "1.2.3") {
        Write-Host "✅ Version parsing works correctly" -ForegroundColor Green
    } else {
        Write-Host "❌ Version parsing failed. Expected '1.2.3', got '$version'" -ForegroundColor Red
    }
    
    # Test version comparison
    $isNewer = Compare-ProfileVersions -Current "1.0.0" -Remote "1.1.0"
    if ($isNewer) {
        Write-Host "✅ Version comparison works correctly" -ForegroundColor Green
    } else {
        Write-Host "❌ Version comparison failed" -ForegroundColor Red
    }
    
    # Test syntax validation
    $validSyntax = Test-PowerShellSyntax -Content "Write-Host 'Valid PowerShell'"
    $invalidSyntax = Test-PowerShellSyntax -Content "Write-Host 'Invalid PowerShell"
    
    if ($validSyntax -and -not $invalidSyntax) {
        Write-Host "✅ PowerShell syntax validation works correctly" -ForegroundColor Green
    } else {
        Write-Host "❌ PowerShell syntax validation failed" -ForegroundColor Red
    }
    
    # Test 3: Test with preview mode (safe test)
    Write-Host "`n3️⃣ Testing preview mode..." -ForegroundColor Yellow
    try {
        Update-PowerShellProfile -Preview -GitHubUsername "chadnpc" -ErrorAction Stop
        Write-Host "✅ Preview mode executed successfully" -ForegroundColor Green
    } catch {
        Write-Host "⚠️  Preview mode test failed: $($_.Exception.Message)" -ForegroundColor Yellow
        Write-Host "   This might be due to network issues or gist not found" -ForegroundColor Gray
    }
    
    # Test 4: Check backup functionality
    Write-Host "`n4️⃣ Testing backup functionality..." -ForegroundColor Yellow
    
    if (Test-Path $PROFILE) {
        $originalSize = (Get-Item $PROFILE).Length
        try {
            Backup-Profile -BackupCount 1
            
            $profileDir = Split-Path $PROFILE -Parent
            $profileName = Split-Path $PROFILE -Leaf
            $backups = Get-ChildItem $profileDir -Filter "$profileName.backup.*"
            
            if ($backups.Count -gt 0) {
                Write-Host "✅ Backup creation works correctly" -ForegroundColor Green
                
                # Clean up test backup
                $backups | Remove-Item -Force
                Write-Host "🧹 Cleaned up test backup" -ForegroundColor Gray
            } else {
                Write-Host "❌ Backup creation failed" -ForegroundColor Red
            }
        } catch {
            Write-Host "❌ Backup test failed: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "⚠️  No existing profile found to test backup functionality" -ForegroundColor Yellow
    }
    
    Write-Host "`n🎉 Testing completed!" -ForegroundColor Cyan
    Write-Host "`n💡 To test with your actual gist, run:" -ForegroundColor Blue
    Write-Host "   Update-PowerShellProfile -Preview" -ForegroundColor Gray
    Write-Host "`n💡 To perform an actual update, run:" -ForegroundColor Blue
    Write-Host "   Update-PowerShellProfile" -ForegroundColor Gray
}

function Show-Usage {
    <#
    .SYNOPSIS
        Shows usage examples for the PowerShell Profile Updater
    #>
    
    Write-Host "📖 PowerShell Profile Updater - Usage Examples" -ForegroundColor Cyan
    Write-Host "===============================================" -ForegroundColor Cyan
    
    $examples = @(
        @{
            Title = "Basic Update (Public Gist)"
            Command = "Update-PowerShellProfile"
            Description = "Updates from default user 'chadnpc' public gist"
        },
        @{
            Title = "Preview Changes"
            Command = "Update-PowerShellProfile -Preview"
            Description = "Shows what would change without applying updates"
        },
        @{
            Title = "Force Update"
            Command = "Update-PowerShellProfile -Force"
            Description = "Updates without confirmation prompts"
        },
        @{
            Title = "Specific User"
            Command = "Update-PowerShellProfile -GitHubUsername 'yourusername'"
            Description = "Updates from a specific GitHub user's gist"
        },
        @{
            Title = "Specific Gist ID"
            Command = "Update-PowerShellProfile -GistId 'abc123def456'"
            Description = "Updates from a specific gist by ID"
        },
        @{
            Title = "With GitHub Token"
            Command = "Update-PowerShellProfile -Token 'ghp_xxxxxxxxxxxx'"
            Description = "Uses GitHub token for private gists or rate limit avoidance"
        },
        @{
            Title = "Custom Settings"
            Command = "Update-PowerShellProfile -GistFileName 'profile.ps1' -BackupCount 5"
            Description = "Custom filename and backup retention settings"
        }
    )
    
    foreach ($example in $examples) {
        Write-Host "`n🔹 $($example.Title)" -ForegroundColor Yellow
        Write-Host "   $($example.Command)" -ForegroundColor Green
        Write-Host "   $($example.Description)" -ForegroundColor Gray
    }
    
    Write-Host "`n📋 Available Parameters:" -ForegroundColor Cyan
    Write-Host "   -GitHubUsername  : GitHub username (default: 'chadnpc')" -ForegroundColor Gray
    Write-Host "   -GistId          : Specific gist ID" -ForegroundColor Gray
    Write-Host "   -GistFileName    : Profile filename in gist" -ForegroundColor Gray
    Write-Host "   -Force           : Skip confirmations" -ForegroundColor Gray
    Write-Host "   -Preview         : Show changes without applying" -ForegroundColor Gray
    Write-Host "   -BackupCount     : Number of backups to keep (default: 3)" -ForegroundColor Gray
    Write-Host "   -Token           : GitHub personal access token" -ForegroundColor Gray
}

# Main execution
if ($args.Count -eq 0) {
    Write-Host "🚀 PowerShell Profile Updater Test Suite" -ForegroundColor Cyan
    Write-Host "=========================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Choose an option:" -ForegroundColor Yellow
    Write-Host "1. Run tests" -ForegroundColor Green
    Write-Host "2. Show usage examples" -ForegroundColor Green
    Write-Host "3. Exit" -ForegroundColor Green
    
    $choice = Read-Host "`nEnter your choice (1-3)"
    
    switch ($choice) {
        "1" { Test-ProfileUpdater }
        "2" { Show-Usage }
        "3" { Write-Host "👋 Goodbye!" -ForegroundColor Cyan }
        default { Write-Host "❌ Invalid choice" -ForegroundColor Red }
    }
} elseif ($args[0] -eq "test") {
    Test-ProfileUpdater
} elseif ($args[0] -eq "usage") {
    Show-Usage
} else {
    Write-Host "Usage: .\Test-ProfileUpdater.ps1 [test|usage]" -ForegroundColor Yellow
}
