

# snippet 1: ----- Check for updates -----
# $profile_update_job = Start-ThreadJob -Name "Get version of `$profile from gist" -ArgumentList $gistUrl, $latestVersionFile, $versionRegEx -ScriptBlock {
#     param ($gistUrl, $latestVersionFile, $versionRegEx)
#     try {
#         $gist = Invoke-RestMethod $gistUrl -ErrorAction Stop
#         $gistProfile = $gist.Files."profile.ps1".Content
#         [version]$gistVersion = "0.0.0"
#         if ($gistProfile -match $versionRegEx) {
#             $gistVersion = $matches.Version
#             Set-Content -Path $latestVersionFile -Value $gistVersion
#         }
#     } catch {
#         # we can hit rate limit issue with GitHub since we're using anonymous
#         Write-Verbose -Verbose "Was not able to access gist to check for newer version"
#     }
# }


# nippet 2 ( old way) :
# check if newer version
# $gistUrl = "https://api.github.com/gists/b712cb340e0491bc7bb981474a65e57b"
# $latestVersionFile = [System.IO.Path]::Combine("$HOME", '.latest_profile_version')
# $versionRegEx = "# Version (?<version>\d+\.\d+\.\d+)"

# if ([System.IO.File]::Exists($latestVersionFile)) {
#     $latestVersion = [System.IO.File]::ReadAllText($latestVersionFile)
#     $currentProfile = [System.IO.File]::ReadAllText($profile)
#     [version]$currentVersion = "0.0.0"
#     if ($currentProfile -match $versionRegEx) {
#         $currentVersion = $matches.Version
#     }

            #     if ([version]$latestVersion -gt $currentVersion) {
            #         Write-Verbose "Your version: $currentVersion" -Verbose
            #         Write-Verbose "New version: $latestVersion" -Verbose
            #         $choice = Read-Host -Prompt "Found newer profile, install? (Y)"
            #         if ($choice -eq "Y" -or $choice -eq "") {
            #             try {
            #                 $gist = Invoke-RestMethod $gistUrl -ErrorAction Stop
            #                 $gistProfile = $gist.Files."profile.ps1".Content
            #                 Set-Content -Path $profile -Value $gistProfile
            #                 Write-Verbose "Installed newer version of profile" -Verbose
            #                 . $profile
#                 return
#             } catch {
#                 # we can hit rate limit issue with GitHub since we're using anonymous
#                 Write-Verbose -Verbose "Was not able to access gist, try again next time"
#             }
#         }
#     }
# }
            
# snippet 3:
#  weakness: to much prompting when launched(thus forced to be interactive), ie: username & password

function Update-PsProfile {
  <#
  .SYNOPSIS
      Updates to your latest Powershell Profile.
  .DESCRIPTION
      Updates your Powershell Profile to latest by downloading it from a Gist.
  .EXAMPLE
      PS C:\> Update-PsProfile
      # Just like that & youll be using my $profile as its the default (u can change to urs if u want. 😊).
  .INPUTS
      [string]
  .OUTPUTS
      files as $profile and $PROFILE.CurrentUserAllHosts. or some crazy errors 🤷‍♂️.
      I wrote this to download from my gist. so ChAnGeIT before use.
  #>
  [CmdletBinding()]
  param (
    # Your Github UserName, By default I use Mine ie: alainQtec
    [Parameter(Mandatory = $false)]
    [string]$GithubUSERNAME = 'chadnpc',
    # The name of the Gist you want to download (In this case the PsProfile).
    [Parameter(Mandatory = $false)]
    [string]$GistFileName = 'Microsoft.PowerShell_profile.ps1'
  )

  begin {
    $GitHubCred = $null
    # $script:GitHubCred = $host.ui.PromptForCredential("GitHub credentials", "Please enter your GitHub Username and password.", "", "NetBiosUserName")
    $script:GitHubCred = Get-Credential -Message "Please enter your GitHub Username and password."
    if ($null -eq $script:GitHubCred) {
      Write-Host "Error. " -ForegroundColor DarkRed -NoNewline; Write-Host "Please Enter Credentials."
      break;
    }
    $ErrorActionPreference = "SilentlyContinue"
    if (!(Test-Path $PROFILE)) {
      New-Item -ItemType File -Path $PROFILE | Out-Null
    }
    $P1 = Get-Item $PROFILE; $P2 = $($P1.DirectoryName + "\" + $P1.BaseName + ".old" );
    if ($P1.Exists -and (Test-Path $P2)) {
      Remove-Item -Path $P2 -Force
    }
    # [Recomended] create a backup.
    Copy-Item -Path "$P1" -Destination "$P2" -Force | Out-Null
    $script:P1old = Get-Item "$P2"
  }

  process {
    # [OPTIONAL] Cleanup all profiles
    $($PROFILE | Get-Member -Force | Where-Object { $_.MemberType -eq "NoteProperty" }).Name | ForEach-Object { if (Test-Path $PROFILE.$_) { Remove-Item $PROFILE.$_ } }
    try {
      $GetAuthHeader = [ScriptBlock]::Create( {
          $authInfo = "{0}:{1}" -f $script:GitHubCred.UserName, $script:GitHubCred.GetNetworkCredential().Password
          $authInfo = [Convert]::ToBase64String([Text.Encoding]::UTF8.GetBytes($authInfo))
          @{
            'Authorization' = 'Basic ' + $authInfo
            'Content-Type'  = 'application/json'
          }
        }
      ); $AuthHeader = Invoke-Command -ScriptBlock $GetAuthHeader
      $Username = $GitHubCred.UserName
      if ("$null" -ne "$Username") {
        $GithubUSERNAME = $Username
      }
      Write-Verbose "Quering the gists..."
      $gists = Invoke-RestMethod -Headers $AuthHeader -Uri "https://api.github.com/users/$GithubUSERNAME/gists"
      # Get the filename
      [string]$FileName = ForEach ($gist in $gists) { Write-Output "$(($gist.files | Get-Member -MemberType NoteProperty).Name | Where-Object { "$_" -match "$GistFileName" })" };
      # Get the required gist
      [PSCustomObject]$reqgist = ForEach ($gist in $gists) { if ("$($gist.files | Get-Member -MemberType NoteProperty).Name)" -match "$GistFileName") { Write-Output $gist } };
      if (("$FileName" -eq "") -or ("$null" -eq "$Filename")) {
        # Throw error & break;
      }
      if (!("$FileName" -match "$GistFileName")) {
        # Throw error & break;
      }
      # Find the actual file within the gist
      $reqgist.files.PSObject.Properties | ForEach-Object {
        if ("$($_.Name.Trim())" -match "$($FileName.Trim())") {
          [PSCustomObject]$script:reqgistFile = Write-Output $_.value
        }
      }
      $gistUri = $reqgistFile.raw_url
      Write-Host "Fetching latest " -NoNewline
      Write-Host "$GistFileName" -ForegroundColor Green -NoNewline
      Write-Host " from https://gist.github.com/$GithubUSERNAME ..."
      # Header will not be necessary (or be correct) if your gist is public
      Invoke-RestMethod -Uri $gistUri -Headers $AuthHeader | Out-File -FilePath $PROFILE
    }
    catch [System.IO.IOException] {
      ""
      Write-Host "Error. " -ForegroundColor DarkRed -NoNewline; Write-Host "Unable to Update Powershell Profile"
      $_.Exception.Message
      ""
      if (Test-Path $P1old) {
        Rename-Item -Path $P1old.FullName -NewName $($P1old.DirectoryName + "\" + $P1old.BaseName + ".ps1" ) | Out-Null
        Remove-Item -Path "$P1old.FullName" -Force | Out-Null
      }
    }
    catch [System.Net.WebException] {
      ""
      Write-Host "Error. " -ForegroundColor DarkRed -NoNewline; Write-Host "No internet Connection"
      ""
      $_.Exception.Message
      ""
      if (Test-Path $P1old) {
        Rename-Item -Path $P1old.FullName -NewName $($P1old.DirectoryName + "\" + $P1old.BaseName + ".ps1" ) | Out-Null
        Remove-Item -Path "$P1old.FullName" -Force | Out-Null
      }
    }
    catch {
      ""
      Write-Host "An error occurred that could not be resolved!" -BackgroundColor DarkYellow -ForegroundColor Black
      $_.Exception.message
      ""
      if (Test-Path $P1old) {
        Rename-Item -Path $P1old.FullName -NewName $($P1old.DirectoryName + "\" + $P1old.BaseName + ".ps1" ) | Out-Null
        Remove-Item -Path "$P1old.FullName" -Force | Out-Null
      }
    }
  }
  end {
    $ErrorActionPreference = "Continue"
  }
}