function Update-PowerShellProfile {
    <#
    .SYNOPSIS
        Updates your PowerShell profile to the latest version from a GitHub Gist.
    
    .DESCRIPTION
        This function downloads the latest version of your PowerShell profile from a GitHub Gist,
        compares it with your current profile, and updates it if a newer version is available.
        It supports both public and private gists with robust error handling and backup functionality.
    
    .PARAMETER GitHubUsername
        The GitHub username that owns the gist. Default is 'chadnpc'.
    
    .PARAMETER GistId
        The specific Gist ID to download from. If not provided, the function will search for gists.
    
    .PARAMETER GistFileName
        The name of the profile file in the gist. Default is 'Microsoft.PowerShell_profile.ps1'.
    
    .PARAMETER Force
        Forces the update without version comparison or user confirmation.
    
    .PARAMETER Preview
        Shows a preview of changes without applying them.
    
    .PARAMETER BackupCount
        Number of backup files to keep. Default is 3.
    
    .PARAMETER Token
        GitHub personal access token for private gists or to avoid rate limiting.
    
    .EXAMPLE
        Update-PowerShellProfile
        Updates the profile using default settings (public gist from 'chadnpc').
    
    .EXAMPLE
        Update-PowerShellProfile -GitHubUsername "myusername" -Preview
        Previews changes from a specific user's gist without applying them.
    
    .EXAMPLE
        Update-PowerShellProfile -GistId "b712cb340e0491bc7bb981474a65e57b" -Force
        Forces update from a specific gist ID.
    
    .EXAMPLE
        Update-PowerShellProfile -Token "ghp_xxxxxxxxxxxx"
        Updates using a GitHub token for authentication.
    #>
    
    [CmdletBinding(SupportsShouldProcess)]
    param(
        [Parameter(Mandatory = $false)]
        [string]$GitHubUsername = 'chadnpc',
        
        [Parameter(Mandatory = $false)]
        [string]$GistId,
        
        [Parameter(Mandatory = $false)]
        [string]$GistFileName = 'Microsoft.PowerShell_profile.ps1',
        
        [Parameter(Mandatory = $false)]
        [switch]$Force,
        
        [Parameter(Mandatory = $false)]
        [switch]$Preview,
        
        [Parameter(Mandatory = $false)]
        [int]$BackupCount = 3,
        
        [Parameter(Mandatory = $false)]
        [string]$Token
    )
    
    begin {
        # Version regex pattern for comparison
        $script:VersionRegex = '# Version (?<Version>\d+\.\d+\.\d+)'
        $script:LastModifiedRegex = '# Last Modified: (?<LastModified>\d{4}-\d{2}-\d{2})'
        
        # Initialize variables
        $script:Headers = @{
            'User-Agent' = 'PowerShell-Profile-Updater/1.0'
            'Accept' = 'application/vnd.github.v3+json'
        }
        
        # Add authentication if token is provided
        if ($Token) {
            $script:Headers['Authorization'] = "token $Token"
        }
        
        Write-Host "🔄 PowerShell Profile Updater" -ForegroundColor Cyan
        Write-Host "================================" -ForegroundColor Cyan
    }
    
    process {
        try {
            # Ensure profile directory exists
            $profileDir = Split-Path $PROFILE -Parent
            if (-not (Test-Path $profileDir)) {
                New-Item -ItemType Directory -Path $profileDir -Force | Out-Null
                Write-Host "✅ Created profile directory: $profileDir" -ForegroundColor Green
            }
            
            # Create profile file if it doesn't exist
            if (-not (Test-Path $PROFILE)) {
                New-Item -ItemType File -Path $PROFILE -Force | Out-Null
                Write-Host "✅ Created profile file: $PROFILE" -ForegroundColor Green
            }
            
            # Get current profile content and version
            $currentContent = Get-Content $PROFILE -Raw -ErrorAction SilentlyContinue
            $currentVersion = Get-ProfileVersion -Content $currentContent
            
            Write-Host "📍 Current profile version: $currentVersion" -ForegroundColor Yellow
            
            # Get gist content
            $gistContent = Get-GistContent -GitHubUsername $GitHubUsername -GistId $GistId -GistFileName $GistFileName
            
            if (-not $gistContent) {
                throw "Failed to retrieve gist content"
            }
            
            # Get remote version
            $remoteVersion = Get-ProfileVersion -Content $gistContent
            Write-Host "🌐 Remote profile version: $remoteVersion" -ForegroundColor Yellow
            
            # Compare versions and content
            $shouldUpdate = $Force -or (Compare-ProfileVersions -Current $currentVersion -Remote $remoteVersion) -or (Compare-ProfileContent -Current $currentContent -Remote $gistContent)
            
            if (-not $shouldUpdate) {
                Write-Host "✅ Profile is already up to date!" -ForegroundColor Green
                return
            }
            
            # Preview mode
            if ($Preview) {
                Show-ProfilePreview -Current $currentContent -Remote $gistContent
                return
            }
            
            # Confirm update unless forced
            if (-not $Force) {
                $confirmation = Read-Host "🤔 Update profile from version $currentVersion to $remoteVersion? (Y/n)"
                if ($confirmation -eq 'n' -or $confirmation -eq 'N') {
                    Write-Host "❌ Update cancelled by user" -ForegroundColor Yellow
                    return
                }
            }
            
            # Validate PowerShell syntax
            if (-not (Test-PowerShellSyntax -Content $gistContent)) {
                throw "Remote profile contains invalid PowerShell syntax"
            }
            
            # Create backup
            Backup-Profile -BackupCount $BackupCount
            
            # Update profile
            if ($PSCmdlet.ShouldProcess($PROFILE, "Update PowerShell Profile")) {
                Set-Content -Path $PROFILE -Value $gistContent -Encoding UTF8
                Write-Host "✅ Profile updated successfully!" -ForegroundColor Green
                
                # Offer to reload profile
                $reload = Read-Host "🔄 Reload profile now? (Y/n)"
                if ($reload -ne 'n' -and $reload -ne 'N') {
                    . $PROFILE
                    Write-Host "✅ Profile reloaded!" -ForegroundColor Green
                }
            }
            
        }
        catch {
            Write-Host "❌ Error updating profile: $($_.Exception.Message)" -ForegroundColor Red
            
            # Attempt to restore from backup if update failed
            $latestBackup = Get-ChildItem (Split-Path $PROFILE -Parent) -Filter "*.backup*" | 
                Sort-Object LastWriteTime -Descending | 
                Select-Object -First 1
            
            if ($latestBackup) {
                $restore = Read-Host "🔄 Restore from latest backup? (Y/n)"
                if ($restore -ne 'n' -and $restore -ne 'N') {
                    Copy-Item $latestBackup.FullName $PROFILE -Force
                    Write-Host "✅ Profile restored from backup" -ForegroundColor Green
                }
            }
        }
    }
}

# Helper function to get profile version
function Get-ProfileVersion {
    param([string]$Content)
    
    if ([string]::IsNullOrEmpty($Content)) {
        return "0.0.0"
    }
    
    if ($Content -match $script:VersionRegex) {
        return $matches.Version
    }
    
    # Fallback to last modified date if no version found
    if ($Content -match $script:LastModifiedRegex) {
        return "1.0.0"  # Assume version 1.0.0 if only date is found
    }
    
    return "0.0.0"
}

# Helper function to compare profile versions
function Compare-ProfileVersions {
    param(
        [string]$Current,
        [string]$Remote
    )
    
    try {
        $currentVer = [version]$Current
        $remoteVer = [version]$Remote
        return $remoteVer -gt $currentVer
    }
    catch {
        # If version comparison fails, assume update is needed
        return $true
    }
}

# Helper function to compare profile content
function Compare-ProfileContent {
    param(
        [string]$Current,
        [string]$Remote
    )
    
    if ([string]::IsNullOrEmpty($Current) -and -not [string]::IsNullOrEmpty($Remote)) {
        return $true
    }
    
    # Simple hash comparison
    $currentHash = (Get-FileHash -InputStream ([System.IO.MemoryStream]::new([System.Text.Encoding]::UTF8.GetBytes($Current)))).Hash
    $remoteHash = (Get-FileHash -InputStream ([System.IO.MemoryStream]::new([System.Text.Encoding]::UTF8.GetBytes($Remote)))).Hash
    
    return $currentHash -ne $remoteHash
}

# Helper function to validate PowerShell syntax
function Test-PowerShellSyntax {
    param([string]$Content)
    
    try {
        $null = [System.Management.Automation.PSParser]::Tokenize($Content, [ref]$null)
        return $true
    }
    catch {
        Write-Warning "PowerShell syntax validation failed: $($_.Exception.Message)"
        return $false
    }
}

# Helper function to create profile backup
function Backup-Profile {
    param([int]$BackupCount = 3)

    if (-not (Test-Path $PROFILE)) {
        return
    }

    $profileDir = Split-Path $PROFILE -Parent
    $profileName = Split-Path $PROFILE -Leaf
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $backupPath = Join-Path $profileDir "$profileName.backup.$timestamp"

    Copy-Item $PROFILE $backupPath -Force
    Write-Host "📁 Created backup: $backupPath" -ForegroundColor Green

    # Clean up old backups
    $backups = Get-ChildItem $profileDir -Filter "$profileName.backup.*" |
        Sort-Object LastWriteTime -Descending

    if ($backups.Count -gt $BackupCount) {
        $backups | Select-Object -Skip $BackupCount | Remove-Item -Force
        Write-Host "🧹 Cleaned up old backups (keeping $BackupCount)" -ForegroundColor Gray
    }
}

# Helper function to get gist content
function Get-GistContent {
    param(
        [string]$GitHubUsername,
        [string]$GistId,
        [string]$GistFileName
    )

    try {
        # If specific gist ID is provided, use it directly
        if ($GistId) {
            $gistUrl = "https://api.github.com/gists/$GistId"
            Write-Host "🔍 Fetching gist by ID: $GistId" -ForegroundColor Yellow
        }
        else {
            # Search for gists by username
            Write-Host "🔍 Searching gists for user: $GitHubUsername" -ForegroundColor Yellow
            $gistsUrl = "https://api.github.com/users/$GitHubUsername/gists"

            # Try public gists first
            try {
                $gists = Invoke-RestMethod -Uri $gistsUrl -Headers $script:Headers -ErrorAction Stop

                # Find gist containing the profile file
                $targetGist = $gists | Where-Object {
                    $_.files.PSObject.Properties.Name -contains $GistFileName
                } | Select-Object -First 1

                if (-not $targetGist) {
                    throw "No gist found containing file: $GistFileName"
                }

                $gistUrl = $targetGist.url
                Write-Host "✅ Found gist: $($targetGist.id)" -ForegroundColor Green
            }
            catch {
                # If public search fails, prompt for authentication
                Write-Host "⚠️  Public gist search failed. Gist may be private." -ForegroundColor Yellow

                if (-not $script:Headers.ContainsKey('Authorization')) {
                    $token = Read-Host "Enter GitHub token for private gist access (or press Enter to skip)" -AsSecureString
                    if ($token.Length -gt 0) {
                        $tokenPlain = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($token))
                        $script:Headers['Authorization'] = "token $tokenPlain"

                        # Retry with authentication
                        $gists = Invoke-RestMethod -Uri $gistsUrl -Headers $script:Headers -ErrorAction Stop
                        $targetGist = $gists | Where-Object {
                            $_.files.PSObject.Properties.Name -contains $GistFileName
                        } | Select-Object -First 1

                        if (-not $targetGist) {
                            throw "No gist found containing file: $GistFileName"
                        }

                        $gistUrl = $targetGist.url
                    }
                    else {
                        throw "Authentication required for private gist access"
                    }
                }
            }
        }

        # Get the specific gist
        $gist = Invoke-RestMethod -Uri $gistUrl -Headers $script:Headers -ErrorAction Stop

        # Find the profile file in the gist
        $profileFile = $gist.files.PSObject.Properties | Where-Object {
            $_.Name -eq $GistFileName -or $_.Name -like "*profile*"
        } | Select-Object -First 1

        if (-not $profileFile) {
            throw "Profile file '$GistFileName' not found in gist"
        }

        Write-Host "📥 Downloading: $($profileFile.Name)" -ForegroundColor Green

        # Get the raw content
        $rawUrl = $profileFile.Value.raw_url
        $content = Invoke-RestMethod -Uri $rawUrl -Headers $script:Headers -ErrorAction Stop

        return $content
    }
    catch {
        Write-Error "Failed to get gist content: $($_.Exception.Message)"
        return $null
    }
}

# Helper function to show preview of changes
function Show-ProfilePreview {
    param(
        [string]$Current,
        [string]$Remote
    )

    Write-Host "`n📋 Profile Update Preview" -ForegroundColor Cyan
    Write-Host "=========================" -ForegroundColor Cyan

    if ([string]::IsNullOrEmpty($Current)) {
        Write-Host "Current profile is empty or doesn't exist" -ForegroundColor Yellow
        Write-Host "`nNew profile will contain:" -ForegroundColor Green
        Write-Host $Remote.Substring(0, [Math]::Min(500, $Remote.Length)) -ForegroundColor Gray
        if ($Remote.Length -gt 500) {
            Write-Host "... (truncated)" -ForegroundColor Gray
        }
    }
    else {
        # Simple diff-like comparison
        $currentLines = $Current -split "`n"
        $remoteLines = $Remote -split "`n"

        Write-Host "Lines in current profile: $($currentLines.Count)" -ForegroundColor Yellow
        Write-Host "Lines in remote profile: $($remoteLines.Count)" -ForegroundColor Yellow

        # Show first few lines of each for comparison
        Write-Host "`nFirst 10 lines of current profile:" -ForegroundColor Cyan
        $currentLines | Select-Object -First 10 | ForEach-Object { Write-Host "  $_" -ForegroundColor Gray }

        Write-Host "`nFirst 10 lines of remote profile:" -ForegroundColor Green
        $remoteLines | Select-Object -First 10 | ForEach-Object { Write-Host "  $_" -ForegroundColor Gray }
    }

    Write-Host "`n💡 Use -Force to apply changes without confirmation" -ForegroundColor Blue
}
