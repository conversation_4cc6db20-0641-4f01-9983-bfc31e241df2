# PowerShell Profile Updater

An optimized PowerShell script that automatically updates your PowerShell profile from a GitHub Gist with robust error handling, backup functionality, and support for both public and private gists.

## Features

- ✅ **Automatic Version Detection**: Compares local and remote profile versions
- 🔒 **Public & Private Gist Support**: Works with both public gists and private gists (with authentication)
- 📁 **Automatic Backups**: Creates timestamped backups before updates
- 🔍 **Preview Mode**: See changes before applying them
- ✔️ **Syntax Validation**: Validates PowerShell syntax before updating
- 🔄 **Smart Updates**: Only updates when necessary (version or content changes)
- 🛡️ **Error Recovery**: Automatic backup restoration on failed updates
- 📱 **Cross-Platform**: Compatible with PowerShell 5.1 and PowerShell 7+

## Quick Start

### Basic Usage (Public Gist)
```powershell
# Load the function
. .\Update-PowerShellProfile.ps1

# Update profile from default user 'chadnpc'
Update-PowerShellProfile
```

### Advanced Usage Examples

```powershell
# Preview changes before applying
Update-PowerShellProfile -Preview

# Force update without confirmation
Update-PowerShellProfile -Force

# Update from specific user
Update-PowerShellProfile -GitHubUsername "yourusername"

# Update from specific gist ID
Update-PowerShellProfile -GistId "b712cb340e0491bc7bb981474a65e57b"

# Use GitHub token for private gists or rate limit avoidance
Update-PowerShellProfile -Token "ghp_xxxxxxxxxxxxxxxxxxxx"

# Custom filename and backup settings
Update-PowerShellProfile -GistFileName "profile.ps1" -BackupCount 5
```

## Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `GitHubUsername` | String | 'chadnpc' | GitHub username that owns the gist |
| `GistId` | String | - | Specific Gist ID (optional, will search if not provided) |
| `GistFileName` | String | 'Microsoft.PowerShell_profile.ps1' | Name of the profile file in the gist |
| `Force` | Switch | - | Forces update without confirmation |
| `Preview` | Switch | - | Shows preview of changes without applying |
| `BackupCount` | Int | 3 | Number of backup files to keep |
| `Token` | String | - | GitHub personal access token |

## Profile Version Format

For automatic version detection, include a version comment in your profile:

```powershell
# Version 1.2.3
# Last Modified: 2024-01-15

# Your profile content here...
```

## Authentication

### Public Gists
No authentication required. The script will attempt to access public gists first.

### Private Gists
Use one of these methods:

1. **GitHub Token (Recommended)**:
   ```powershell
   Update-PowerShellProfile -Token "ghp_xxxxxxxxxxxxxxxxxxxx"
   ```

2. **Interactive Prompt**: 
   If a public gist search fails, you'll be prompted to enter a token.

### Creating a GitHub Token

1. Go to GitHub Settings → Developer settings → Personal access tokens
2. Generate new token (classic)
3. Select scopes: `gist` (for gist access)
4. Copy the token and use it with the `-Token` parameter

## Error Handling

The script includes comprehensive error handling:

- **Network Issues**: Graceful handling of connection problems
- **Authentication Failures**: Clear error messages and fallback options
- **File Permission Issues**: Automatic backup and restore functionality
- **Invalid Syntax**: PowerShell syntax validation before applying updates
- **Rate Limiting**: Support for GitHub tokens to avoid API limits

## Backup System

- Automatic timestamped backups before each update
- Configurable backup retention (default: 3 backups)
- Automatic cleanup of old backups
- Emergency restore functionality on failed updates

## Comparison with Original Snippets

### Improvements Over Snippet 1 (ThreadJob Version)
- ✅ Synchronous execution (no threading complexity)
- ✅ Better error handling
- ✅ Backup functionality
- ✅ User confirmation prompts

### Improvements Over Snippet 2 (Old Way)
- ✅ No hardcoded gist URLs
- ✅ Better version comparison logic
- ✅ Automatic gist discovery
- ✅ Enhanced error recovery

### Improvements Over Snippet 3 (Interactive Version)
- ✅ Optional authentication (public gists work without prompts)
- ✅ Token-based authentication (more secure than username/password)
- ✅ Better error handling and recovery
- ✅ Preview functionality
- ✅ Syntax validation

## Installation

1. Download the `Update-PowerShellProfile.ps1` file
2. Place it in a convenient location
3. Load the function in your PowerShell session:
   ```powershell
   . .\Update-PowerShellProfile.ps1
   ```

### Add to Profile for Permanent Access
Add this line to your PowerShell profile to make the function always available:
```powershell
. "C:\Path\To\Update-PowerShellProfile.ps1"
```

## Troubleshooting

### Common Issues

**"Failed to retrieve gist content"**
- Check your internet connection
- Verify the GitHub username is correct
- For private gists, ensure you have a valid token

**"Remote profile contains invalid PowerShell syntax"**
- The remote profile has syntax errors
- Use `-Preview` to examine the content
- Fix the gist content before updating

**"No gist found containing file"**
- Verify the gist filename matches exactly
- Check if the gist is public or requires authentication
- Use `-GistId` to specify a particular gist

### Rate Limiting
If you encounter GitHub API rate limits:
- Use a GitHub token with the `-Token` parameter
- Wait for the rate limit to reset (usually 1 hour)

## Contributing

Feel free to submit issues and enhancement requests!

## License

This script is provided as-is for educational and personal use.
